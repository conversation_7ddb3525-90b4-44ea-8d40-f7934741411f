package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.detect.Detect")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测活动")
public class DetectDTO extends BaseDTO<DetectDTO> {
    
    @Schema(description = "检测名称")
    private String name;
    
    @Schema(description = "检测编号")
    private String code;
    
    @Schema(description = "所属项目ID")
    private Long projectId;
    
    @Schema(description = "检测状态")
    private DetectStatus status;
    
    @Schema(description = "检测类型")
    private DetectType type;
    
    @Schema(description = "检测位置")
    private String location;
    
    @Schema(description = "检测时间")
    private LocalDateTime detectTime;
    
    @Schema(description = "检测人员")
    private String detector;
    
    @Schema(description = "备注")
    private String remark;
    
    @Getter
    public enum DetectStatus implements TitleEnum {
        PENDING("待检测"),
        IN_PROGRESS("检测中"),
        COMPLETED("已完成"),
        FAILED("检测失败");
        
        private final String title;
        
        DetectStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum DetectType implements TitleEnum {
        WATER_QUALITY("水质检测"),
        AIR_QUALITY("空气质量检测"),
        NOISE_LEVEL("噪音检测"),
        STRUCTURAL("结构检测"),
        ELECTRICAL("电气检测"),
        FIRE_SAFETY("消防安全检测");
        
        private final String title;
        
        DetectType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
