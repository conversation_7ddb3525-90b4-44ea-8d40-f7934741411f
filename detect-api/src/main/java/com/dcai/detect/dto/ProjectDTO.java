package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.project.Project")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测项目")
public class ProjectDTO extends BaseDTO<ProjectDTO> {
    
    @Schema(description = "项目名称")
    private String name;
    
    @Schema(description = "项目编号")
    private String code;
    
    @Schema(description = "项目描述")
    private String description;
    
    @Schema(description = "项目状态")
    private ProjectStatus status;
    
    @Schema(description = "社区信息")
    private String community;
    
    @Schema(description = "委托机构ID")
    private Long clientOrganId;
    
    @Schema(description = "检测机构ID")
    private Long detectOrganId;
    
    @Schema(description = "监管机构ID")
    private Long supervisionOrganId;
    
    @Schema(description = "计划开始时间")
    private LocalDateTime plannedStartTime;
    
    @Schema(description = "计划结束时间")
    private LocalDateTime plannedEndTime;
    
    @Schema(description = "实际开始时间")
    private LocalDateTime actualStartTime;
    
    @Schema(description = "实际结束时间")
    private LocalDateTime actualEndTime;
    
    @Getter
    public enum ProjectStatus implements TitleEnum {
        DRAFT("草稿"),
        APPROVED("已审批"),
        IN_PROGRESS("进行中"),
        COMPLETED("已完成"),
        CANCELLED("已取消");
        
        private final String title;
        
        ProjectStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
