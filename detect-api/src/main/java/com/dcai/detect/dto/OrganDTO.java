package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.organ.Organ")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "机构信息")
public class OrganDTO extends BaseDTO<OrganDTO> {
    
    @Schema(description = "机构名称")
    private String name;
    
    @Schema(description = "机构代码")
    private String code;
    
    @Schema(description = "机构类型")
    private OrganType type;
    
    @Schema(description = "联系电话")
    private String phone;
    
    @Schema(description = "联系地址")
    private String address;
    
    @Schema(description = "机构状态")
    private OrganStatus status;
    
    @Getter
    public enum OrganType implements TitleEnum {
        PROPERTY_COMPANY("物业公司"),
        DETECT_AGENCY("检测机构"),
        SUPERVISION_AGENCY("监管机构"),
        PROPERTY_PLACE("物业场所");
        
        private final String title;
        
        OrganType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum OrganStatus implements TitleEnum {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String title;
        
        OrganStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
