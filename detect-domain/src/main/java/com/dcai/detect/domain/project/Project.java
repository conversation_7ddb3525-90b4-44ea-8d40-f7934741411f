package com.dcai.detect.domain.project;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.dcai.detect.dto.ProjectDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("检测项目")
@Table(name = "tb_project", uniqueConstraints = @UniqueConstraint(name = "uk_project_code", columnNames = {"code"}))
@Where(clause = "logic_delete = 0")
public class Project extends BaseEntity<Project> implements Serializable {

    @Id
    @Comment("项目主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '项目名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '项目编号'", nullable = false, unique = true)
    private String code;

    @Column(name = "description", columnDefinition = "text COMMENT '项目描述'")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '项目状态'", nullable = false)
    private ProjectDTO.ProjectStatus status;

    @Column(name = "community", columnDefinition = "varchar(127) COMMENT '社区信息'")
    private String community;

    @Column(name = "client_organ_id", columnDefinition = "bigint(20) COMMENT '委托机构ID'", nullable = false)
    private Long clientOrganId;

    @Column(name = "detect_organ_id", columnDefinition = "bigint(20) COMMENT '检测机构ID'", nullable = false)
    private Long detectOrganId;

    @Column(name = "supervision_organ_id", columnDefinition = "bigint(20) COMMENT '监管机构ID'")
    private Long supervisionOrganId;

    @Column(name = "planned_start_time", columnDefinition = "datetime COMMENT '计划开始时间'")
    private LocalDateTime plannedStartTime;

    @Column(name = "planned_end_time", columnDefinition = "datetime COMMENT '计划结束时间'")
    private LocalDateTime plannedEndTime;

    @Column(name = "actual_start_time", columnDefinition = "datetime COMMENT '实际开始时间'")
    private LocalDateTime actualStartTime;

    @Column(name = "actual_end_time", columnDefinition = "datetime COMMENT '实际结束时间'")
    private LocalDateTime actualEndTime;

    public Project(String name, String code, String description, String community,
                   Long clientOrganId, Long detectOrganId, Long supervisionOrganId,
                   LocalDateTime plannedStartTime, LocalDateTime plannedEndTime) {
        this.name = name;
        this.code = code;
        this.description = description;
        this.community = community;
        this.clientOrganId = clientOrganId;
        this.detectOrganId = detectOrganId;
        this.supervisionOrganId = supervisionOrganId;
        this.plannedStartTime = plannedStartTime;
        this.plannedEndTime = plannedEndTime;
        this.status = ProjectDTO.ProjectStatus.DRAFT;
    }

    public void approve() {
        if (!ProjectDTO.ProjectStatus.DRAFT.equals(this.status)) {
            throw new BusinessException("只有草稿状态的项目才能审批");
        }
        this.status = ProjectDTO.ProjectStatus.APPROVED;
    }

    public void start() {
        if (!ProjectDTO.ProjectStatus.APPROVED.equals(this.status)) {
            throw new BusinessException("只有已审批的项目才能开始");
        }
        this.status = ProjectDTO.ProjectStatus.IN_PROGRESS;
        this.actualStartTime = LocalDateTime.now();
    }

    public void complete() {
        if (!ProjectDTO.ProjectStatus.IN_PROGRESS.equals(this.status)) {
            throw new BusinessException("只有进行中的项目才能完成");
        }
        this.status = ProjectDTO.ProjectStatus.COMPLETED;
        this.actualEndTime = LocalDateTime.now();
    }

    public void cancel() {
        if (ProjectDTO.ProjectStatus.COMPLETED.equals(this.status)) {
            throw new BusinessException("已完成的项目不能取消");
        }
        this.status = ProjectDTO.ProjectStatus.CANCELLED;
    }

    public boolean canModify() {
        return ProjectDTO.ProjectStatus.DRAFT.equals(this.status);
    }
}
