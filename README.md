# 检测模块 (Detect Module)

基于领域驱动设计(DDD)的物业检测管理系统，支持多机构协作的检测业务流程。

## 项目架构

### 模块结构
```
detect/
├── detect-api/          # API接口定义和DTO
├── detect-domain/       # 领域模型和业务逻辑
├── detect-application/  # 应用层和启动类
├── detect-integration/  # 外部集成
└── detect-sdk/         # SDK工具
```

### 技术栈
- **框架**: Spring Boot 3.x + Spring Cloud
- **数据库**: MySQL + Redis
- **ORM**: JPA/Hibernate
- **服务发现**: Nacos
- **API文档**: Swagger/OpenAPI 3
- **构建工具**: Maven

## 领域模型

### 核心实体

#### 1. 机构管理 (Organ)
- **物业公司**: 委托检测的机构
- **检测机构**: 执行检测任务的专业机构
- **监管机构**: 监督检测过程的政府部门
- **物业场所**: 被检测的物业项目

#### 2. 项目管理 (Project)
- 检测项目的全生命周期管理
- 支持多机构协作
- 状态流转：草稿 → 已审批 → 进行中 → 已完成

#### 3. 检测活动 (Detect)
- 具体的检测执行活动
- 关联检测项目和检测项
- 支持多种检测类型

#### 4. 检测项 (Item)
- 标准化的检测项目定义
- 支持多种数据类型
- 可配置检测方法和标准值

#### 5. 表单系统 (Form)
- **采集表单**: 现场数据采集
- **报告表单**: 检测结果报告
- 支持动态表单配置

#### 6. 设备管理 (Device)
- **采集设备**: 数据采集工具
- **分析器**: 数据分析处理
- **生成器**: 报告生成工具

## 业务流程

### 1. 项目创建流程
1. 物业公司创建检测项目
2. 指定检测机构和监管机构
3. 设置检测计划和要求
4. 提交审批

### 2. 检测执行流程
1. 项目审批通过后开始执行
2. 检测机构安排检测活动
3. 使用设备采集数据
4. 填写采集表单
5. 数据分析处理
6. 生成检测报告

### 3. 质量监管流程
1. 监管机构监督检测过程
2. 审核检测结果
3. 质量评估和反馈

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 启动步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd detect
```

2. **配置数据库**
```yaml
# application-local.yaml
spring:
  datasource:
    url: ***************************************
    username: your_username
    password: your_password
```

3. **启动应用**
```bash
mvn clean install
cd detect-application
mvn spring-boot:run
```

4. **访问接口文档**
```
http://localhost:8022/detect/swagger-ui.html
```

## API接口

### 机构管理
- `POST /api/organ/create` - 创建机构
- `GET /api/organ/list` - 查询机构列表
- `PUT /api/organ/update/{id}` - 更新机构信息

### 项目管理
- `POST /api/project/create` - 创建项目
- `GET /api/project/list` - 查询项目列表
- `POST /api/project/approve/{id}` - 审批项目

### 检测管理
- `POST /api/detect/create` - 创建检测活动
- `GET /api/detect/list` - 查询检测列表
- `POST /api/detect/complete/{id}` - 完成检测

## 配置说明

### 数据库配置
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用，生产环境改为validate
    show-sql: true      # 开发环境显示SQL
```

### 缓存配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 5000
```

### 服务发现配置
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dcai
```

## 开发指南

### 代码规范
1. 遵循DDD设计原则
2. 实体类继承BaseEntity
3. 使用Repository模式访问数据
4. API接口使用统一响应格式

### 数据库设计
- 表名使用`tb_`前缀
- 字段使用下划线命名
- 必须包含逻辑删除字段
- 添加适当的索引和约束

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test
```

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t detect-app .

# 运行容器
docker run -p 8022:8022 detect-app
```

### 生产环境配置
- 修改数据库连接配置
- 调整JVM参数
- 配置日志输出
- 设置监控告警

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License
