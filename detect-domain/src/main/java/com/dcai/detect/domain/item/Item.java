package com.dcai.detect.domain.item;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.ItemDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("检测项")
@Table(name = "tb_item", uniqueConstraints = @UniqueConstraint(name = "uk_item_code", columnNames = {"code"}))
@Where(clause = "logic_delete = 0")
public class Item extends BaseEntity<Item> implements Serializable {

    @Id
    @Comment("检测项主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '检测项名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '检测项代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "description", columnDefinition = "text COMMENT '检测项描述'")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "data_type", columnDefinition = "varchar(31) COMMENT '数据类型'", nullable = false)
    private ItemDTO.DataType dataType;

    @Column(name = "unit", columnDefinition = "varchar(31) COMMENT '单位'")
    private String unit;

    @Column(name = "standard_range", columnDefinition = "varchar(127) COMMENT '标准值范围'")
    private String standardRange;

    @Column(name = "method", columnDefinition = "varchar(255) COMMENT '检测方法'")
    private String method;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '检测项状态'", nullable = false)
    private ItemDTO.ItemStatus status;

    public Item(String name, String code, String description, ItemDTO.DataType dataType,
                String unit, String standardRange, String method) {
        this.name = name;
        this.code = code;
        this.description = description;
        this.dataType = dataType;
        this.unit = unit;
        this.standardRange = standardRange;
        this.method = method;
        this.status = ItemDTO.ItemStatus.ACTIVE;
    }

    public void updateInfo(String name, String description, String unit, String standardRange, String method) {
        this.name = name;
        this.description = description;
        this.unit = unit;
        this.standardRange = standardRange;
        this.method = method;
    }

    public void enable() {
        this.status = ItemDTO.ItemStatus.ACTIVE;
    }

    public void disable() {
        this.status = ItemDTO.ItemStatus.INACTIVE;
    }

    public boolean isActive() {
        return ItemDTO.ItemStatus.ACTIVE.equals(this.status);
    }
}
